<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON>hu<PERSON></title>
    <!-- T<PERSON><PERSON> hợp <PERSON>lwind CSS để tạo giao diện nhanh chóng -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- T<PERSON><PERSON> hợ<PERSON> Google Fonts (Inter) cho văn bản dễ đọc -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* CSS tùy chỉnh */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6; /* <PERSON><PERSON><PERSON> nền xám nhạt cho trang web */
        }
        /* <PERSON><PERSON>u hình cho khổ giấy A5 khi in */
        @page {
            size: A5;
            margin: 1cm;
        }
        /* Tối ưu hóa giao diện khi in */
        @media print {
            body {
                background-color: #fff; /* Nền trắng khi in */
            }
            .prescription-container {
                box-shadow: none;
                margin: 0;
                max-width: 100%;
                border: none;
            }
            .no-print {
                display: none; /* Ẩn các thành phần không cần in như nút "In đơn" */
            }
        }
        /* Giữ cho bảng không bị vỡ trang khi in */
        table {
            page-break-inside: auto;
        }
        tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }
        thead {
            display: table-header-group;
        }
        tfoot {
            display: table-footer-group;
        }
    </style>
</head>
<body class="p-4 md:p-8">

    <!-- Nút In Đơn Thuốc (Sẽ bị ẩn khi in) -->
    <div class="max-w-4xl mx-auto mb-4 text-right no-print">
        <button onclick="window.print()" class="bg-blue-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-300">
            🖨️ In đơn thuốc
        </button>
    </div>

    <!-- Khung chứa toàn bộ đơn thuốc -->
    <div id="prescription" class="prescription-container max-w-4xl mx-auto bg-white p-6 md:p-8 rounded-lg shadow-lg border border-gray-200">
        
        <!-- PHẦN ĐẦU: Thông tin phòng khám và mã đơn thuốc -->
        <header class="flex justify-between items-start pb-4 border-b-2 border-gray-200">
            <!-- Thông tin cơ sở khám bệnh, chữa bệnh -->
            <div class="text-xs">
                <h2 class="font-bold text-sm uppercase text-blue-700">PHÒNG KHÁM ĐA KHOA ABC</h2>
                <p class="mt-1"><strong>Địa chỉ:</strong> Số 123, Đường XYZ, Phường Cống Vị, Quận Ba Đình, Hà Nội</p>
                <p><strong>Điện thoại:</strong> (024) 3456 7890</p>
                <!-- Đã cập nhật Mã CSKCB để nhất quán với mã đơn thuốc -->
                <p><strong>Mã CSKCB:</strong> 01234</p>
            </div>
            <!-- Mã đơn thuốc và QR Code -->
            <div class="text-center">
                <p class="text-xs">Mã đơn thuốc:</p>
                <!-- ĐÃ CẬP NHẬT: Mã đơn thuốc theo định dạng mới -->
                <p class="font-mono font-bold text-sm">01234a1b2c3d-C</p>
                <!-- Thay thế src bằng QR code thật được tạo từ hệ thống -->
                <img src="https://placehold.co/100x100/e2e8f0/333?text=QR+CODE" 
                     alt="QR Code tra cứu đơn thuốc" 
                     class="w-24 h-24 mt-1"
                     onerror="this.onerror=null;this.src='https://placehold.co/100x100/e2e8f0/333?text=QR+Error';">
            </div>
        </header>

        <!-- TIÊU ĐỀ ĐƠN THUỐC -->
        <div class="text-center my-6">
            <h1 class="text-2xl md:text-3xl font-bold uppercase">ĐƠN THUỐC</h1>
            <!-- Ghi chú: Đối với thuốc gây nghiện, hướng thần, cần thay đổi tiêu đề tương ứng -->
            <!-- Ví dụ: <h1 class="text-2xl font-bold uppercase">ĐƠN THUỐC "N"</h1> -->
        </div>

        <!-- PHẦN THÔNG TIN BỆNH NHÂN -->
        <section class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-3 text-sm">
            <div><strong>Họ và tên:</strong> NGUYỄN VĂN A</div>
            <div><strong>Ngày sinh:</strong> 01/01/1985 (39 tuổi)</div>
            <div><strong>Giới tính:</strong> Nam</div>
            <div><strong>Số CCCD/Định danh:</strong> 012345678912</div>
            <div><strong>Cân nặng:</strong> 70 kg</div>
            <div class="md:col-span-2"><strong>Địa chỉ:</strong> Số 10, Ngõ 50, Phố Liễu Giai, Phường Liễu Giai, Quận Ba Đình, Hà Nội</div>
            <div class="md:col-span-2"><strong>Số thẻ BHYT:</strong> HN 2 0123456789 (nếu có)</div>
            <div class="md:col-span-2 bg-gray-100 p-2 rounded-md">
                <strong class="text-red-600">Chẩn đoán:</strong> Viêm họng cấp (J02.9)
            </div>
        </section>

        <!-- PHẦN KÊ ĐƠN THUỐC -->
        <section class="mt-6">
            <table class="w-full border-collapse text-sm">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border p-2 text-center font-semibold">TT</th>
                        <th class="border p-2 text-left font-semibold">Tên thuốc, nồng độ, hàm lượng</th>
                        <th class="border p-2 text-center font-semibold">ĐVT</th>
                        <th class="border p-2 text-center font-semibold">SL</th>
                        <th class="border p-2 text-left font-semibold">Liều dùng - Cách dùng</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Dòng 1: Dữ liệu thuốc mẫu -->
                    <tr>
                        <td class="border p-2 text-center">1</td>
                        <td class="border p-2">
                            <strong>Paracetamol 500mg</strong>
                            <br>
                            <span class="text-xs text-gray-600">(Tên biệt dược: Panadol)</span>
                        </td>
                        <td class="border p-2 text-center">Viên</td>
                        <td class="border p-2 text-center">20</td>
                        <td class="border p-2">Uống 1 viên/lần, 3-4 lần/ngày sau ăn khi sốt trên 38.5°C.</td>
                    </tr>
                    <!-- Dòng 2: Dữ liệu thuốc mẫu -->
                    <tr>
                        <td class="border p-2 text-center">2</td>
                        <td class="border p-2">
                            <strong>Amoxicilin 500mg</strong>
                        </td>
                        <td class="border p-2 text-center">Viên</td>
                        <td class="border p-2 text-center">14</td>
                        <td class="border p-2">Uống 1 viên/lần, 2 lần/ngày (sáng, tối) sau ăn. Uống hết liều.</td>
                    </tr>
                    <!-- Dòng 3: Dữ liệu thuốc mẫu -->
                     <tr>
                        <td class="border p-2 text-center">3</td>
                        <td class="border p-2">
                            <strong>Alpha chymotrypsin 4.2mg</strong>
                        </td>
                        <td class="border p-2 text-center">Viên</td>
                        <td class="border p-2 text-center">30</td>
                        <td class="border p-2">Ngậm dưới lưỡi 2 viên/lần, 3-4 lần/ngày.</td>
                    </tr>
                    <!-- Thêm các dòng thuốc khác tại đây -->
                </tbody>
            </table>
        </section>

        <!-- LỜI DẶN CỦA BÁC SĨ -->
        <section class="mt-6 text-sm">
            <p><strong>Lời dặn của bác sĩ:</strong></p>
            <ul class="list-disc list-inside pl-4 mt-1">
                <li>Uống nhiều nước ấm, nghỉ ngơi, giữ ấm cổ.</li>
                <li>Hạn chế đồ ăn cay nóng, dầu mỡ, nước đá lạnh.</li>
                <li>Súc họng bằng nước muối sinh lý hàng ngày.</li>
                <li>Nếu có dấu hiệu bất thường (phát ban, khó thở, sốt cao không hạ), cần đến cơ sở y tế gần nhất ngay.</li>
            </ul>
        </section>

        <!-- PHẦN CUỐI: Ngày tháng, chữ ký -->
        <footer class="mt-8 pt-4">
            <div class="flex justify-between items-start">
                <!-- Thông tin người nhận thuốc -->
                <div class="text-center w-1/2">
                    <p class="font-semibold">Người nhận thuốc/Người nhà</p>
                    <p class="text-xs italic">(Ký, ghi rõ họ tên)</p>
                    <div class="h-20"></div> <!-- Khoảng trống để ký tên -->
                </div>

                <!-- Thông tin bác sĩ kê đơn -->
                <div class="text-center w-1/2">
                    <p class="text-sm"><em>Ngày 11 tháng 07 năm 2025</em></p>
                    <p class="font-semibold mt-1">Bác sĩ/Y sĩ kê đơn</p>
                    <div class="h-12 flex items-center justify-center">
                        <!-- Đây là nơi hiển thị thông tin chữ ký số đã được xác thực -->
                        <span class="italic text-green-600">-- Đã ký số --</span>
                    </div>
                    <p class="font-bold text-base">BS.CKI. TRẦN THỊ B</p>
                    <p class="text-xs">Số GPHN: 001234/BYT-CCHN</p>
                </div>
            </div>
            <div class="mt-4 text-center text-xs text-gray-500">
                <p><strong>Hẹn tái khám:</strong> Sau 7 ngày hoặc khi có dấu hiệu bất thường.</p>
                <p class="mt-2">Vui lòng mang theo đơn này khi tái khám.</p>
            </div>
        </footer>
    </div>
</body>
</html>
