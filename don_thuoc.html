<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON>hu<PERSON></title>
    <!-- T<PERSON><PERSON> hợp <PERSON>lwind CSS để tạo giao diện nhanh chóng -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- T<PERSON><PERSON> hợ<PERSON> Google Fonts (Inter) cho văn bản dễ đọc -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* CSS tùy chỉnh */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6; /* <PERSON><PERSON><PERSON> nền xám nhạt cho trang web */
        }
        /* <PERSON><PERSON>u hình cho khổ giấy A5 khi in */
        @page {
            size: A5;
            margin: 1cm;
        }
        /* Tối ưu hóa giao diện khi in */
        @media print {
            body {
                background-color: #fff; /* Nền trắng khi in */
            }
            .prescription-container {
                box-shadow: none;
                margin: 0;
                max-width: 100%;
                border: none;
            }
            .no-print {
                display: none; /* Ẩn các thành phần không cần in như nút "In đơn" */
            }
        }
        /* Giữ cho bảng không bị vỡ trang khi in */
        table {
            page-break-inside: auto;
        }
        tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }
        thead {
            display: table-header-group;
        }
        tfoot {
            display: table-footer-group;
        }
    </style>
</head>
<body class="p-4 md:p-8">

    <!-- Nút In Đơn Thuốc (Sẽ bị ẩn khi in) -->
    <div class="max-w-4xl mx-auto mb-4 text-right no-print">
        <button onclick="window.print()" class="bg-blue-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-blue-700 transition duration-300">
            🖨️ In đơn thuốc
        </button>
    </div>

    <!-- Khung chứa toàn bộ đơn thuốc -->
    <div id="prescription" class="prescription-container max-w-4xl mx-auto bg-white p-6 md:p-8 rounded-lg shadow-lg border border-gray-200">
        
        <!-- PHẦN ĐẦU: Thông tin phòng khám và mã đơn thuốc -->
        <header class="flex justify-between items-start pb-4 border-b-2 border-gray-200">
            <!-- Thông tin cơ sở khám bệnh, chữa bệnh -->
            <div class="text-xs">
                <h2 class="font-bold text-sm uppercase text-blue-700">PHÒNG KHÁM ĐA KHOA ABC</h2>
                <p class="mt-1"><strong>Địa chỉ:</strong> Số 123, Đường XYZ, Phường Ninh Kiều, Thành phố Cần Thơ </p>
                <p><strong>Điện thoại:</strong> (024) 3456 7890</p>
                <p><strong>Mã CSKCB:</strong> 01234</p>
            </div>
            <!-- Mã đơn thuốc và QR Code -->
            <div class="text-center">
                <p class="text-xs">Mã đơn thuốc:</p>
                <p class="font-mono font-bold text-sm">01234a1b2c3d-C</p>
                <!-- Thay thế src bằng QR code thật được tạo từ hệ thống -->
                <img src="https://placehold.co/100x100/e2e8f0/333?text=QR+CODE" 
                     alt="QR Code tra cứu đơn thuốc" 
                     class="w-24 h-24 mt-1"
                     onerror="this.onerror=null;this.src='https://placehold.co/100x100/e2e8f0/333?text=QR+Error';">
            </div>
        </header>

        <!-- TIÊU ĐỀ ĐƠN THUỐC -->
        <div class="text-center my-6">
            <h1 class="text-2xl md:text-3xl font-bold uppercase">ĐƠN THUỐC</h1>
            <!-- Ghi chú: Đối với thuốc gây nghiện, hướng thần, cần thay đổi tiêu đề tương ứng -->
            <!-- Ví dụ: <h1 class="text-2xl font-bold uppercase">ĐƠN THUỐC "N"</h1> -->
        </div>

        <!-- PHẦN THÔNG TIN BỆNH NHÂN (DỮ LIỆU ĐỘNG) -->
        <section class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-3 text-sm">
            <div><strong>Họ và tên:</strong> <span id="patient-name"><!-- Dữ liệu động --></span></div>
            <div><strong>Ngày sinh:</strong> <span id="patient-dob"><!-- Dữ liệu động --></span> (<span id="patient-age"></span> tuổi)</div>
            <div><strong>Giới tính:</strong> <span id="patient-gender"><!-- Dữ liệu động --></span></div>
            <div><strong>Số CCCD/Định danh:</strong> <span id="patient-id-card"><!-- Dữ liệu động --></span></div>
            <div><strong>Cân nặng:</strong> <span id="patient-weight"><!-- Dữ liệu động --></span> kg</div>
            <div class="md:col-span-2"><strong>Địa chỉ:</strong> <span id="patient-address"><!-- Dữ liệu động --></span></div>
            <div class="md:col-span-2"><strong>Số thẻ BHYT:</strong> <span id="patient-bhyt"><!-- Dữ liệu động --></span></div>
            <div class="md:col-span-2 bg-gray-100 p-2 rounded-md">
                <strong class="text-red-600" id="diagnosis-content">
                    Chẩn đoán: <!-- Backend sẽ chèn chẩn đoán vào đây -->
                </strong>
            </div>
        </section>

        <!-- PHẦN KÊ ĐƠN THUỐC -->
        <section class="mt-6">
            <table class="w-full border-collapse text-sm">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border p-2 text-center font-semibold">TT</th>
                        <th class="border p-2 text-left font-semibold">Tên thuốc, nồng độ, hàm lượng</th>
                        <th class="border p-2 text-center font-semibold">ĐVT</th>
                        <th class="border p-2 text-center font-semibold">SL</th>
                        <th class="border p-2 text-left font-semibold">Liều dùng - Cách dùng</th>
                    </tr>
                </thead>
                <tbody id="drug-list-body">
                    <!-- 
                        Backend sẽ lặp qua danh sách thuốc và tạo các dòng <tr> theo cấu trúc sau:
                        <tr>
                            <td class="border p-2 text-center">[Số thứ tự]</td>
                            <td class="border p-2">
                                <strong>[Tên thuốc, hàm lượng]</strong>
                                <br>
                                <span class="text-xs text-gray-600">(Tên biệt dược: [Tên biệt dược nếu có])</span>
                            </td>
                            <td class="border p-2 text-center">[Đơn vị tính]</td>
                            <td class="border p-2 text-center">[Số lượng]</td>
                            <td class="border p-2">[Liều dùng - Cách dùng]</td>
                        </tr>
                    -->
                </tbody>
            </table>
        </section>

        <!-- LỜI DẶN CỦA BÁC SĨ -->
        <section class="mt-6 text-sm">
            <p><strong>Lời dặn của bác sĩ:</strong></p>
            <ul class="list-disc list-inside pl-4 mt-1" id="doctor-advice-list">
                <!-- Backend sẽ lặp và chèn các mục lời dặn (<li>) vào đây -->
            </ul>
        </section>

        <!-- PHẦN CUỐI: Ngày tháng, chữ ký -->
        <footer class="mt-8 pt-4">
            <div class="flex justify-between items-start">
                <!-- Thông tin người nhận thuốc -->
                <div class="text-center w-1/2">
                    <p class="font-semibold">Người nhận thuốc/Người nhà</p>
                    <p class="text-xs italic">(Ký, ghi rõ họ tên)</p>
                    <div class="h-24"></div> <!-- Khoảng trống để ký tên -->
                </div>

                <!-- Thông tin bác sĩ kê đơn -->
                <div class="text-center w-1/2">
                    <!-- ĐÃ CẬP NHẬT: Ngày kê đơn sẽ được điền động -->
                    <p class="text-sm"><em id="prescription-date"><!-- Ngày kê đơn động, ví dụ: Ngày 11 tháng 07 năm 2025 --></em></p>
                    <p class="font-semibold mt-1">Bác sĩ kê đơn</p>
                    <div class="h-16 flex items-center justify-center">
                        <!-- Đây là nơi hiển thị thông tin chữ ký số đã được xác thực -->
                        <span class="italic text-green-600">-- Đã ký số --</span>
                    </div>
                    <p class="font-bold text-base">BS.CKI. TRẦN THỊ B</p>
                    <p class="text-xs">Số GPHN: 001234/BYT-CCHN</p>
                </div>
            </div>
            <div class="mt-4 text-center text-xs text-gray-500">
                <p><strong>Hẹn tái khám:</strong> Sau 7 ngày hoặc khi có dấu hiệu bất thường.</p>
                <p class="mt-2">Vui lòng mang theo đơn này khi tái khám.</p>
            </div>
        </footer>
    </div>
</body>
</html>
