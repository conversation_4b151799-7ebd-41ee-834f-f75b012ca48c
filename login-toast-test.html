<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Toast Notification Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom animations for toast */
        @keyframes slideInFromTop {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        @keyframes slideInFromBottom {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        .toast-enter {
            animation: slideInFromTop 0.3s ease-out;
        }
        
        @media (min-width: 640px) {
            .toast-enter {
                animation: slideInFromBottom 0.3s ease-out;
            }
        }
        
        .toast-exit {
            animation: slideInFromTop 0.3s ease-out reverse;
        }
        
        @media (min-width: 640px) {
            .toast-exit {
                animation: slideInFromBottom 0.3s ease-out reverse;
            }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Login Form Demo -->
    <div class="min-h-screen bg-gray-100 flex flex-col">
        <!-- Header Section -->
        <header class="bg-green-600 text-white py-8 px-4 text-center shadow-md">
            <div class="inline-block bg-white p-2 rounded-full mb-3">
                <svg class="w-12 h-12 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                </svg>
            </div>
            <h1 class="text-2xl font-bold tracking-wider">
                QUẢN LÝ PHÒNG KHÁM
            </h1>
            <p class="text-sm text-green-100 mt-1">
                Đăng nhập vào hệ thống
            </p>
        </header>

        <!-- Form Section -->
        <main class="flex-grow flex items-center justify-center p-4">
            <div class="w-full max-w-sm -mt-24 shadow-xl bg-white rounded-lg">
                <div class="p-6 pt-8 space-y-4">
                    <!-- Login Form -->
                    <form class="space-y-4" onsubmit="handleLogin(event)">
                        <div class="space-y-2">
                            <label class="flex items-center text-sm font-medium text-gray-700">
                                <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                Tên đăng nhập
                            </label>
                            <input
                                type="email"
                                placeholder="Nhập email của bạn"
                                value="<EMAIL>"
                                class="w-full h-11 text-base px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                required
                            />
                        </div>

                        <div class="space-y-2">
                            <label class="flex items-center text-sm font-medium text-gray-700">
                                <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                                Mật khẩu
                            </label>
                            <input
                                type="password"
                                placeholder="Nhập mật khẩu của bạn"
                                value="111"
                                class="w-full h-11 text-base px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                required
                            />
                        </div>

                        <button
                            type="submit"
                            class="w-full h-11 text-base font-semibold bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors"
                        >
                            Đăng nhập
                        </button>
                    </form>

                    <!-- Demo Info -->
                    <div class="p-3 bg-gray-50 rounded-lg border border-gray-200 text-center mt-4">
                        <p class="text-sm text-gray-700 font-medium mb-2">Thông tin demo:</p>
                        <p class="text-xs text-gray-600 mb-1">
                            Email: <span class="font-mono bg-white px-1 rounded"><EMAIL></span>
                        </p>
                        <p class="text-xs text-gray-600">
                            Mật khẩu: <span class="font-mono bg-white px-1 rounded">111</span>
                        </p>
                    </div>
                </div>
                <div class="flex justify-center py-4 border-t">
                    <p class="text-xs text-gray-500">
                        Phát triển bởi <span class="font-medium text-gray-700">Nguyễn Thiện Chí</span>
                    </p>
                </div>
            </div>
        </main>
    </div>

    <!-- Toast Container -->
    <div id="toast-container" class="fixed top-4 left-1/2 -translate-x-1/2 z-50 w-full max-w-sm px-4 sm:top-auto sm:bottom-4 sm:right-4 sm:left-auto sm:translate-x-0 sm:max-w-md">
        <!-- Toast will be inserted here -->
    </div>

    <script>
        function showToast(title, description, variant = 'success') {
            const container = document.getElementById('toast-container');
            
            // Create toast element
            const toast = document.createElement('div');
            toast.className = `
                toast-enter
                relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg
                ${variant === 'success' ? 'border-green-200 bg-green-50 text-green-900' : 'border bg-white text-gray-900'}
            `;
            
            toast.innerHTML = `
                <div class="grid gap-1 flex-1">
                    <div class="flex items-center gap-2">
                        ${variant === 'success' ? `
                            <svg class="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        ` : ''}
                        <div class="text-sm font-semibold">${title}</div>
                    </div>
                    ${description ? `<div class="text-sm opacity-90">${description}</div>` : ''}
                </div>
                <button onclick="closeToast(this)" class="absolute right-2 top-2 rounded-md p-1 text-gray-500 opacity-70 transition-opacity hover:opacity-100 focus:opacity-100 focus:outline-none focus:ring-2">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            `;
            
            // Add to container
            container.appendChild(toast);
            
            // Auto-dismiss after 3 seconds
            setTimeout(() => {
                closeToast(toast.querySelector('button'));
            }, 3000);
        }
        
        function closeToast(button) {
            const toast = button.closest('[class*="toast-"]');
            if (toast) {
                toast.classList.remove('toast-enter');
                toast.classList.add('toast-exit');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }
        }
        
        function handleLogin(event) {
            event.preventDefault();
            
            // Simulate login success
            setTimeout(() => {
                showToast(
                    'Đăng nhập thành công!',
                    'Chào mừng Bác sĩ Minh quay trở lại!',
                    'success'
                );
            }, 500);
        }
        
        // Test different screen sizes
        function testResponsiveToast() {
            showToast(
                'Test Toast',
                'Thử nghiệm toast responsive - thay đổi kích thước màn hình để xem sự khác biệt!',
                'success'
            );
        }
        
        // Add test button
        document.addEventListener('DOMContentLoaded', function() {
            const testButton = document.createElement('button');
            testButton.textContent = 'Test Toast Responsive';
            testButton.className = 'fixed bottom-4 left-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors z-40';
            testButton.onclick = testResponsiveToast;
            document.body.appendChild(testButton);
        });
    </script>
</body>
</html>
