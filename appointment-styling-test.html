<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appointment Styling Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom styles to match the application */
        .appointment-card {
            background-color: #E8F5E9; /* Light green background */
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-gray-800">Appointment Management Interface - New Design</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Timeline View -->
            <div>
                <h2 class="text-xl font-semibold mb-4 text-gray-700">Timeline View</h2>
                <div class="space-y-3">
                    <!-- Scheduled Appointment -->
                    <div class="appointment-card border-l-4 border-l-green-600 p-4 rounded-lg shadow-sm">
                        <div class="flex items-center gap-2">
                            <p class="font-semibold text-gray-800">John Doe</p>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">09:00 - 09:30</p>
                        <span class="inline-block mt-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Scheduled</span>
                    </div>
                    
                    <!-- Completed Appointment -->
                    <div class="appointment-card border-l-4 border-l-blue-600 p-4 rounded-lg shadow-sm opacity-70">
                        <div class="flex items-center gap-2">
                            <p class="font-semibold text-gray-800">Jane Smith</p>
                            <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">10:00 - 10:30</p>
                        <span class="inline-block mt-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Completed</span>
                    </div>
                    
                    <!-- Cancelled Appointment -->
                    <div class="appointment-card border-l-4 border-l-red-600 p-4 rounded-lg shadow-sm opacity-50">
                        <div class="flex items-center gap-2">
                            <p class="font-semibold text-gray-800 line-through">Bob Johnson</p>
                        </div>
                        <p class="text-sm text-gray-600 mt-1 line-through">11:00 - 11:30</p>
                        <span class="inline-block mt-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded line-through">Cancelled</span>
                    </div>
                </div>
            </div>
            
            <!-- Table View -->
            <div>
                <h2 class="text-xl font-semibold mb-4 text-gray-700">Table View</h2>
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">Patient</th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">Status</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <tr>
                                <td class="px-4 py-3 text-sm text-gray-800">John Doe</td>
                                <td class="px-4 py-3">
                                    <span class="appointment-card border-l-4 border-l-green-600 text-green-800 px-3 py-1 rounded text-sm">
                                        Scheduled
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm text-gray-800">Jane Smith</td>
                                <td class="px-4 py-3">
                                    <span class="appointment-card border-l-4 border-l-blue-600 text-blue-800 px-3 py-1 rounded text-sm opacity-70">
                                        Completed
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-4 py-3 text-sm text-gray-800">Bob Johnson</td>
                                <td class="px-4 py-3">
                                    <span class="appointment-card border-l-4 border-l-red-600 text-red-800 px-3 py-1 rounded text-sm opacity-50 line-through">
                                        Cancelled
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Design Principles -->
        <div class="mt-12 bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4 text-gray-700">Design Principles Applied</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium text-gray-800 mb-2">✅ Harmonious and Professional</h3>
                    <p class="text-sm text-gray-600">Unified light green background (#E8F5E9) for all appointment blocks creates visual consistency.</p>
                </div>
                <div>
                    <h3 class="font-medium text-gray-800 mb-2">✅ Clear and Easily Recognizable</h3>
                    <p class="text-sm text-gray-600">Left border colors (Green: Scheduled, Blue: Completed, Red: Cancelled) provide instant status recognition.</p>
                </div>
                <div>
                    <h3 class="font-medium text-gray-800 mb-2">✅ Information-Focused</h3>
                    <p class="text-sm text-gray-600">Patient names and times are prominent on subtle backgrounds, not overshadowed by bold colors.</p>
                </div>
                <div>
                    <h3 class="font-medium text-gray-800 mb-2">✅ Modern and Flexible</h3>
                    <p class="text-sm text-gray-600">Subtle accents with opacity effects and icons create a modern, scalable design system.</p>
                </div>
            </div>
        </div>
        
        <!-- Color Legend -->
        <div class="mt-8 bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4 text-gray-700">Status Color Legend</h2>
            <div class="flex flex-wrap gap-4">
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-green-600 rounded"></div>
                    <span class="text-sm text-gray-700">Scheduled (#28A745)</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-blue-600 rounded"></div>
                    <span class="text-sm text-gray-700">Completed (#0D6EFD)</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-red-600 rounded"></div>
                    <span class="text-sm text-gray-700">Cancelled (#DC3545)</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-green-50 border border-gray-300 rounded"></div>
                    <span class="text-sm text-gray-700">Background (#E8F5E9)</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
